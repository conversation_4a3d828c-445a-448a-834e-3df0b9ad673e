#include <Arduino.h>

// 串口通信相关变量
String inputString = "";         // 存储接收到的字符串
bool stringComplete = false;     // 字符串接收完成标志
unsigned long lastHeartbeat = 0; // 上次心跳时间
const unsigned long heartbeatInterval = 5000; // 心跳间隔(毫秒)

// LED引脚定义 (ESP32-C3内置LED通常在GPIO8)
const int LED_PIN = 8;
bool ledState = false;

// 函数声明
void processCommand(String command);
void sendHeartbeat();
void handleSerialInput();

void setup() {
  // 初始化串口通信，波特率115200
  Serial.begin(115200);

  // 等待串口连接
  while (!Serial) {
    delay(10);
  }

  // 初始化LED引脚
  pinMode(LED_PIN, OUTPUT);
  digitalWrite(LED_PIN, LOW);

  // 发送启动信息
  Serial.println("=== ESP32-C3 串口通信程序启动 ===");
  Serial.println("可用命令:");
  Serial.println("  LED_ON  - 打开LED");
  Serial.println("  LED_OFF - 关闭LED");
  Serial.println("  STATUS  - 获取设备状态");
  Serial.println("  RESET   - 重启设备");
  Serial.println("  HELP    - 显示帮助信息");
  Serial.println("================================");

  // 预留字符串空间
  inputString.reserve(200);

  // 记录启动时间
  lastHeartbeat = millis();
}

void loop() {
  // 处理串口输入
  handleSerialInput();

  // 处理完整的命令
  if (stringComplete) {
    processCommand(inputString);
    inputString = "";
    stringComplete = false;
  }

  // 发送心跳信号
  if (millis() - lastHeartbeat >= heartbeatInterval) {
    sendHeartbeat();
    lastHeartbeat = millis();
  }

  // 短暂延时
  delay(10);
}

// 处理串口输入
void handleSerialInput() {
  while (Serial.available()) {
    char inChar = (char)Serial.read();

    // 如果收到换行符，表示字符串接收完成
    if (inChar == '\n' || inChar == '\r') {
      if (inputString.length() > 0) {
        stringComplete = true;
      }
    } else {
      // 将字符添加到输入字符串
      inputString += inChar;
    }
  }
}

// 处理接收到的命令
void processCommand(String command) {
  // 去除首尾空格并转换为大写
  command.trim();
  command.toUpperCase();

  Serial.println("收到命令: " + command);

  if (command == "LED_ON") {
    digitalWrite(LED_PIN, HIGH);
    ledState = true;
    Serial.println("LED已打开");

  } else if (command == "LED_OFF") {
    digitalWrite(LED_PIN, LOW);
    ledState = false;
    Serial.println("LED已关闭");

  } else if (command == "STATUS") {
    Serial.println("=== 设备状态 ===");
    Serial.println("设备: ESP32-C3");
    Serial.println("LED状态: " + String(ledState ? "开启" : "关闭"));
    Serial.println("运行时间: " + String(millis() / 1000) + " 秒");
    Serial.println("自由内存: " + String(ESP.getFreeHeap()) + " 字节");
    Serial.println("===============");

  } else if (command == "RESET") {
    Serial.println("设备即将重启...");
    delay(1000);
    ESP.restart();

  } else if (command == "HELP") {
    Serial.println("=== 帮助信息 ===");
    Serial.println("LED_ON  - 打开LED灯");
    Serial.println("LED_OFF - 关闭LED灯");
    Serial.println("STATUS  - 显示设备状态信息");
    Serial.println("RESET   - 重启ESP32设备");
    Serial.println("HELP    - 显示此帮助信息");
    Serial.println("===============");

  } else if (command.startsWith("ECHO ")) {
    // 回显命令，返回接收到的内容
    String echoText = command.substring(5);
    Serial.println("回显: " + echoText);

  } else {
    Serial.println("未知命令: " + command);
    Serial.println("输入 HELP 查看可用命令");
  }
}

// 发送心跳信号
void sendHeartbeat() {
  Serial.println("HEARTBEAT:" + String(millis()));
}