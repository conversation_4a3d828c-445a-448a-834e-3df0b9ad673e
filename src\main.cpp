#include <Arduino.h>

// 串口通信demo
// 功能：与电脑进行双向串口通信
// 发送：定期发送传感器数据和状态信息
// 接收：处理电脑发送的命令

// 全局变量
unsigned long lastSendTime = 0;
const unsigned long sendInterval = 2000; // 每2秒发送一次数据
int ledPin = 2; // ESP32-C3内置LED引脚
bool ledState = false;
int counter = 0;

void setup() {
  // 初始化串口通信，波特率115200
  Serial.begin(115200);

  // 等待串口连接（可选，用于调试）
  while (!Serial) {
    delay(10);
  }

  // 初始化LED引脚
  pinMode(ledPin, OUTPUT);
  digitalWrite(ledPin, LOW);

  // 发送启动信息
  Serial.println("=== ESP32-C3 串口通信Demo ===");
  Serial.println("系统已启动，准备通信...");
  Serial.println("支持的命令：");
  Serial.println("  LED_ON  - 打开LED");
  Serial.println("  LED_OFF - 关闭LED");
  Serial.println("  STATUS  - 查询状态");
  Serial.println("  RESET   - 重置计数器");
  Serial.println("=============================");
}

void loop() {
  // 处理接收到的串口数据
  handleSerialInput();

  // 定期发送数据到电脑
  sendPeriodicData();

  // 其他任务
  delay(10);
}

// 处理串口输入
void handleSerialInput() {
  if (Serial.available() > 0) {
    String command = Serial.readStringUntil('\n');
    command.trim(); // 去除首尾空白字符
    command.toUpperCase(); // 转换为大写

    Serial.println("收到命令: " + command);

    // 处理不同的命令
    if (command == "LED_ON") {
      digitalWrite(ledPin, HIGH);
      ledState = true;
      Serial.println("LED已打开");
    }
    else if (command == "LED_OFF") {
      digitalWrite(ledPin, LOW);
      ledState = false;
      Serial.println("LED已关闭");
    }
    else if (command == "STATUS") {
      sendStatusInfo();
    }
    else if (command == "RESET") {
      counter = 0;
      Serial.println("计数器已重置");
    }
    else {
      Serial.println("未知命令: " + command);
      Serial.println("请输入有效命令：LED_ON, LED_OFF, STATUS, RESET");
    }
  }
}

// 定期发送数据
void sendPeriodicData() {
  unsigned long currentTime = millis();

  if (currentTime - lastSendTime >= sendInterval) {
    lastSendTime = currentTime;
    counter++;

    // 模拟传感器数据
    float temperature = 20.0 + random(-50, 51) / 10.0; // 15-25度随机温度
    int humidity = 40 + random(0, 41); // 40-80%随机湿度

    // 发送JSON格式数据
    Serial.println("--- 定期数据 ---");
    Serial.println("{");
    Serial.println("  \"timestamp\": " + String(currentTime) + ",");
    Serial.println("  \"counter\": " + String(counter) + ",");
    Serial.println("  \"temperature\": " + String(temperature, 1) + ",");
    Serial.println("  \"humidity\": " + String(humidity) + ",");
    Serial.println("  \"led_state\": " + String(ledState ? "true" : "false") + ",");
    Serial.println("  \"free_heap\": " + String(ESP.getFreeHeap()));
    Serial.println("}");
    Serial.println("---------------");
  }
}

// 发送状态信息
void sendStatusInfo() {
  Serial.println("=== 系统状态 ===");
  Serial.println("设备: ESP32-C3");
  Serial.println("运行时间: " + String(millis() / 1000) + " 秒");
  Serial.println("LED状态: " + String(ledState ? "开启" : "关闭"));
  Serial.println("计数器: " + String(counter));
  Serial.println("可用内存: " + String(ESP.getFreeHeap()) + " 字节");
  Serial.println("芯片ID: " + String((uint32_t)ESP.getEfuseMac(), HEX));
  Serial.println("===============");
}