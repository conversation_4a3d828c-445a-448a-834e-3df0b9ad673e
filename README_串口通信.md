# ESP32-C3 串口通信Demo

这是一个简单的ESP32-C3与电脑进行串口通信的示例项目。

## 功能特性

### ESP32端功能
- 🔄 **双向通信**：可以接收电脑发送的命令，也可以主动发送数据
- 📊 **定期数据发送**：每2秒自动发送模拟的传感器数据（温度、湿度等）
- 💡 **LED控制**：通过串口命令控制板载LED的开关
- 📋 **状态查询**：可以查询设备运行状态和系统信息
- 🔢 **计数器功能**：内置计数器，可以重置

### 电脑端功能
- 🖥️ **Python测试脚本**：提供完整的PC端通信测试程序
- 📨 **命令发送**：可以发送各种控制命令到ESP32
- 📥 **数据接收**：实时接收并显示ESP32发送的数据
- 📊 **JSON解析**：自动解析JSON格式的传感器数据

## 硬件要求

- ESP32-C3开发板
- USB数据线
- 电脑（Windows/Linux/Mac）

## 软件要求

### ESP32端
- PlatformIO IDE 或 Arduino IDE
- ESP32开发环境

### 电脑端
- Python 3.6+
- pyserial库：`pip install pyserial`

## 使用方法

### 1. 烧录ESP32固件

1. 打开PlatformIO项目
2. 连接ESP32-C3开发板到电脑
3. 编译并上传代码：
   ```bash
   pio run --target upload
   ```

### 2. 运行电脑端测试程序

1. 安装Python依赖：
   ```bash
   pip install pyserial
   ```

2. 修改串口号：
   - 打开 `pc_serial_test.py`
   - 修改 `COM_PORT` 变量为实际的串口号
     - Windows: `COM3`, `COM4` 等
     - Linux: `/dev/ttyUSB0`, `/dev/ttyACM0` 等
     - Mac: `/dev/cu.usbserial-xxx` 等

3. 运行测试程序：
   ```bash
   python pc_serial_test.py
   ```

### 3. 使用串口监视器（可选）

也可以使用PlatformIO的串口监视器：
```bash
pio device monitor
```

## 支持的命令

| 命令 | 功能 | 示例 |
|------|------|------|
| `LED_ON` | 打开LED | 发送: `LED_ON` |
| `LED_OFF` | 关闭LED | 发送: `LED_OFF` |
| `STATUS` | 查询设备状态 | 发送: `STATUS` |
| `RESET` | 重置计数器 | 发送: `RESET` |

## 数据格式

### ESP32发送的定期数据（JSON格式）
```json
{
  "timestamp": 12345,
  "counter": 10,
  "temperature": 23.5,
  "humidity": 65,
  "led_state": true,
  "free_heap": 234567
}
```

### 字段说明
- `timestamp`: 系统运行时间（毫秒）
- `counter`: 计数器值
- `temperature`: 模拟温度值（°C）
- `humidity`: 模拟湿度值（%）
- `led_state`: LED状态（true/false）
- `free_heap`: 可用内存（字节）

## 配置说明

### 串口配置
- **波特率**: 115200
- **数据位**: 8
- **停止位**: 1
- **校验位**: 无

### 发送间隔
- **定期数据**: 每2秒发送一次
- 可在代码中修改 `sendInterval` 变量调整间隔

## 故障排除

### 常见问题

1. **无法连接串口**
   - 检查USB线是否连接正常
   - 确认串口号是否正确
   - 检查是否有其他程序占用串口

2. **收不到数据**
   - 确认波特率设置正确（115200）
   - 检查ESP32是否正常运行
   - 尝试重启ESP32

3. **命令无响应**
   - 确认命令格式正确（大小写不敏感）
   - 检查串口连接是否稳定
   - 查看ESP32串口输出是否有错误信息

### 调试技巧

1. **使用串口监视器**：
   ```bash
   pio device monitor --baud 115200
   ```

2. **查看设备列表**：
   - Windows: 设备管理器 → 端口
   - Linux: `ls /dev/tty*`
   - Mac: `ls /dev/cu.*`

## 扩展建议

1. **添加更多传感器**：可以连接真实的温湿度传感器
2. **增加更多命令**：添加更多控制功能
3. **数据存储**：在电脑端保存接收到的数据
4. **图形界面**：开发GUI应用程序
5. **网络功能**：添加WiFi通信功能

## 许可证

本项目仅供学习和参考使用。
