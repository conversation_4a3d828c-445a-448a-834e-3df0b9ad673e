#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ESP32-C3 串口通信客户端
用于与ESP32-C3设备进行串口通信
"""

import serial
import serial.tools.list_ports
import threading
import time
import sys

class ESP32SerialClient:
    def __init__(self, port=None, baudrate=115200):
        self.port = port
        self.baudrate = baudrate
        self.serial_conn = None
        self.is_connected = False
        self.receive_thread = None
        self.stop_thread = False
        
    def list_ports(self):
        """列出所有可用的串口"""
        ports = serial.tools.list_ports.comports()
        print("可用串口:")
        for i, port in enumerate(ports):
            print(f"  {i+1}. {port.device} - {port.description}")
        return ports
    
    def connect(self, port=None):
        """连接到串口"""
        if port:
            self.port = port
            
        if not self.port:
            ports = self.list_ports()
            if not ports:
                print("未找到可用串口!")
                return False
                
            try:
                choice = int(input("请选择串口编号: ")) - 1
                if 0 <= choice < len(ports):
                    self.port = ports[choice].device
                else:
                    print("无效选择!")
                    return False
            except ValueError:
                print("请输入有效数字!")
                return False
        
        try:
            self.serial_conn = serial.Serial(
                port=self.port,
                baudrate=self.baudrate,
                timeout=1,
                write_timeout=1
            )
            self.is_connected = True
            print(f"成功连接到 {self.port}")
            
            # 启动接收线程
            self.stop_thread = False
            self.receive_thread = threading.Thread(target=self._receive_data)
            self.receive_thread.daemon = True
            self.receive_thread.start()
            
            return True
            
        except serial.SerialException as e:
            print(f"连接失败: {e}")
            return False
    
    def disconnect(self):
        """断开串口连接"""
        if self.is_connected:
            self.stop_thread = True
            if self.receive_thread:
                self.receive_thread.join(timeout=2)
            
            if self.serial_conn:
                self.serial_conn.close()
            
            self.is_connected = False
            print("已断开连接")
    
    def send_command(self, command):
        """发送命令到ESP32"""
        if not self.is_connected:
            print("未连接到设备!")
            return False
            
        try:
            # 发送命令，添加换行符
            self.serial_conn.write((command + '\n').encode('utf-8'))
            print(f"发送: {command}")
            return True
        except serial.SerialException as e:
            print(f"发送失败: {e}")
            return False
    
    def _receive_data(self):
        """接收数据线程"""
        while not self.stop_thread and self.is_connected:
            try:
                if self.serial_conn.in_waiting > 0:
                    data = self.serial_conn.readline().decode('utf-8').strip()
                    if data:
                        print(f"接收: {data}")
            except serial.SerialException:
                break
            except UnicodeDecodeError:
                print("接收到无效字符")
            
            time.sleep(0.01)
    
    def interactive_mode(self):
        """交互模式"""
        print("\n=== 进入交互模式 ===")
        print("可用命令:")
        print("  LED_ON    - 打开LED")
        print("  LED_OFF   - 关闭LED") 
        print("  STATUS    - 获取状态")
        print("  RESET     - 重启设备")
        print("  HELP      - 显示帮助")
        print("  ECHO <文本> - 回显测试")
        print("  quit      - 退出程序")
        print("==================")
        
        while self.is_connected:
            try:
                command = input("请输入命令: ").strip()
                
                if command.lower() == 'quit':
                    break
                elif command == '':
                    continue
                else:
                    self.send_command(command)
                    
            except KeyboardInterrupt:
                print("\n收到中断信号，退出...")
                break
            except EOFError:
                break

def main():
    print("ESP32-C3 串口通信客户端")
    print("=" * 30)
    
    client = ESP32SerialClient()
    
    try:
        # 连接到设备
        if not client.connect():
            return
        
        # 等待设备启动
        time.sleep(2)
        
        # 进入交互模式
        client.interactive_mode()
        
    except KeyboardInterrupt:
        print("\n程序被中断")
    finally:
        client.disconnect()

if __name__ == "__main__":
    main()
