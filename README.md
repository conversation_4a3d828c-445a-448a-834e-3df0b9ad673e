# ESP32-C3 串口通信系统

这是一个完整的ESP32-C3与电脑串口通信系统，包含设备端固件和电脑端客户端程序。

## 功能特性

### ESP32端功能
- 串口通信 (115200波特率)
- LED控制 (开/关)
- 设备状态查询
- 心跳信号发送
- 命令处理系统
- 设备重启功能

### 电脑端功能
- 自动检测串口设备
- 交互式命令界面
- 实时数据接收显示
- 多线程通信处理

## 硬件要求

- ESP32-C3开发板
- USB数据线
- 电脑 (Windows/Linux/macOS)

## 软件要求

### ESP32端
- PlatformIO IDE
- Arduino框架

### 电脑端
- Python 3.6+
- pyserial库

## 安装和使用

### 1. 烧录ESP32固件

1. 打开PlatformIO IDE
2. 打开本项目文件夹
3. 连接ESP32-C3开发板到电脑
4. 点击"Upload"按钮烧录固件

### 2. 运行电脑端客户端

#### Windows用户
双击运行 `run_client.bat` 文件

#### Linux/macOS用户
```bash
# 安装依赖
pip install pyserial

# 运行客户端
python3 pc_serial_client.py
```

### 3. 使用说明

1. 启动客户端后，程序会自动扫描可用串口
2. 选择ESP32对应的串口进行连接
3. 连接成功后进入交互模式
4. 输入命令与ESP32通信

## 可用命令

| 命令 | 功能 | 示例 |
|------|------|------|
| `LED_ON` | 打开LED灯 | `LED_ON` |
| `LED_OFF` | 关闭LED灯 | `LED_OFF` |
| `STATUS` | 获取设备状态 | `STATUS` |
| `RESET` | 重启ESP32设备 | `RESET` |
| `HELP` | 显示帮助信息 | `HELP` |
| `ECHO <文本>` | 回显测试 | `ECHO Hello World` |
| `quit` | 退出客户端程序 | `quit` |

## 通信协议

- 波特率: 115200
- 数据位: 8
- 停止位: 1
- 校验位: 无
- 命令结束符: `\n` 或 `\r\n`

## 心跳机制

ESP32每5秒发送一次心跳信号，格式为：
```
HEARTBEAT:<运行时间毫秒数>
```

## 故障排除

### 连接问题
1. 确认ESP32正确连接到电脑
2. 检查驱动程序是否正确安装
3. 确认串口未被其他程序占用

### 通信问题
1. 检查波特率设置是否正确
2. 确认固件已正确烧录
3. 重启ESP32设备

### Python环境问题
1. 确认Python版本 >= 3.6
2. 安装pyserial: `pip install pyserial`
3. 检查串口权限 (Linux/macOS)

## 扩展开发

### 添加新命令
1. 在ESP32的 `processCommand()` 函数中添加新的命令处理逻辑
2. 更新帮助信息
3. 重新烧录固件

### 修改通信参数
1. 修改 `platformio.ini` 中的 `monitor_speed`
2. 修改 `main.cpp` 中的 `Serial.begin()` 参数
3. 修改Python客户端中的 `baudrate` 参数

## 项目结构

```
USB-communication/
├── src/
│   └── main.cpp              # ESP32主程序
├── include/                  # 头文件目录
├── lib/                      # 库文件目录
├── test/                     # 测试文件目录
├── platformio.ini            # PlatformIO配置
├── pc_serial_client.py       # Python客户端
├── run_client.bat           # Windows启动脚本
└── README.md                # 说明文档
```

## 许可证

本项目采用MIT许可证，详见LICENSE文件。
