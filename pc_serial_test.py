#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ESP32-C3 串口通信测试脚本
用于与ESP32-C3开发板进行串口通信测试

使用方法：
1. 确保ESP32已连接并烧录了对应的固件
2. 修改下面的COM_PORT为实际的串口号
3. 运行脚本：python pc_serial_test.py

支持的命令：
- LED_ON: 打开LED
- LED_OFF: 关闭LED  
- STATUS: 查询状态
- RESET: 重置计数器
- quit: 退出程序
"""

import serial
import time
import threading
import json
from datetime import datetime

# 配置参数
COM_PORT = 'COM3'  # 根据实际情况修改串口号 (Windows: COM3, Linux: /dev/ttyUSB0, Mac: /dev/cu.usbserial-xxx)
BAUD_RATE = 115200
TIMEOUT = 1

class SerialCommunicator:
    def __init__(self, port, baudrate, timeout=1):
        self.port = port
        self.baudrate = baudrate
        self.timeout = timeout
        self.serial_conn = None
        self.running = False
        self.receive_thread = None
        
    def connect(self):
        """连接串口"""
        try:
            self.serial_conn = serial.Serial(
                port=self.port,
                baudrate=self.baudrate,
                timeout=self.timeout,
                bytesize=serial.EIGHTBITS,
                parity=serial.PARITY_NONE,
                stopbits=serial.STOPBITS_ONE
            )
            print(f"成功连接到 {self.port}, 波特率: {self.baudrate}")
            return True
        except serial.SerialException as e:
            print(f"连接失败: {e}")
            return False
    
    def disconnect(self):
        """断开串口连接"""
        self.running = False
        if self.receive_thread and self.receive_thread.is_alive():
            self.receive_thread.join(timeout=2)
        
        if self.serial_conn and self.serial_conn.is_open:
            self.serial_conn.close()
            print("串口连接已断开")
    
    def send_command(self, command):
        """发送命令"""
        if self.serial_conn and self.serial_conn.is_open:
            try:
                self.serial_conn.write((command + '\n').encode('utf-8'))
                print(f"发送命令: {command}")
                return True
            except serial.SerialException as e:
                print(f"发送失败: {e}")
                return False
        else:
            print("串口未连接")
            return False
    
    def receive_data(self):
        """接收数据线程"""
        while self.running and self.serial_conn and self.serial_conn.is_open:
            try:
                if self.serial_conn.in_waiting > 0:
                    data = self.serial_conn.readline().decode('utf-8', errors='ignore').strip()
                    if data:
                        timestamp = datetime.now().strftime("%H:%M:%S")
                        print(f"[{timestamp}] 接收: {data}")
                        
                        # 尝试解析JSON数据
                        if data.startswith('{') and data.endswith('}'):
                            try:
                                json_data = json.loads(data)
                                self.parse_json_data(json_data)
                            except json.JSONDecodeError:
                                pass  # 不是有效的JSON，继续正常显示
                
                time.sleep(0.01)  # 避免CPU占用过高
            except serial.SerialException as e:
                print(f"接收数据错误: {e}")
                break
            except Exception as e:
                print(f"未知错误: {e}")
                break
    
    def parse_json_data(self, data):
        """解析JSON格式的传感器数据"""
        if 'temperature' in data and 'humidity' in data:
            temp = data.get('temperature', 0)
            humidity = data.get('humidity', 0)
            counter = data.get('counter', 0)
            led_state = data.get('led_state', False)
            free_heap = data.get('free_heap', 0)
            
            print(f"    📊 传感器数据 - 温度: {temp}°C, 湿度: {humidity}%, 计数: {counter}")
            print(f"    💡 LED状态: {'开启' if led_state else '关闭'}, 可用内存: {free_heap} 字节")
    
    def start_receiving(self):
        """启动接收线程"""
        self.running = True
        self.receive_thread = threading.Thread(target=self.receive_data, daemon=True)
        self.receive_thread.start()

def print_help():
    """打印帮助信息"""
    print("\n=== 可用命令 ===")
    print("LED_ON   - 打开LED")
    print("LED_OFF  - 关闭LED")
    print("STATUS   - 查询设备状态")
    print("RESET    - 重置计数器")
    print("help     - 显示此帮助")
    print("quit     - 退出程序")
    print("================\n")

def main():
    print("ESP32-C3 串口通信测试程序")
    print("=" * 40)
    
    # 创建串口通信对象
    comm = SerialCommunicator(COM_PORT, BAUD_RATE, TIMEOUT)
    
    # 尝试连接
    if not comm.connect():
        print("无法连接到串口，请检查：")
        print("1. ESP32是否已连接到电脑")
        print("2. 串口号是否正确")
        print("3. 是否有其他程序占用串口")
        return
    
    # 启动接收线程
    comm.start_receiving()
    
    print("连接成功！开始通信...")
    print("输入 'help' 查看可用命令，输入 'quit' 退出")
    print("-" * 40)
    
    try:
        while True:
            # 获取用户输入
            user_input = input("请输入命令: ").strip()
            
            if user_input.lower() == 'quit':
                break
            elif user_input.lower() == 'help':
                print_help()
            elif user_input:
                comm.send_command(user_input)
            
            time.sleep(0.1)
    
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序错误: {e}")
    finally:
        comm.disconnect()
        print("程序结束")

if __name__ == "__main__":
    main()
