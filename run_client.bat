@echo off
echo 启动ESP32串口通信客户端...
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python
    pause
    exit /b 1
)

REM 检查pyserial是否安装
python -c "import serial" >nul 2>&1
if errorlevel 1 (
    echo 正在安装pyserial库...
    pip install pyserial
    if errorlevel 1 (
        echo 错误: 安装pyserial失败
        pause
        exit /b 1
    )
)

REM 运行客户端
python pc_serial_client.py

pause
